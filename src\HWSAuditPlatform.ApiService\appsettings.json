{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=HWSAuditPlatformDb;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"}, "Jwt": {"Key": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "HWSAuditPlatform", "Audience": "HWSAuditPlatformUsers"}, "FileStorage": {"Type": "Local"}, "LocalFileStorage": {"StoragePath": "wwwroot/uploads", "BaseUrl": "https://localhost:5001"}, "AzureBlobStorage": {"ContainerName": "audit-attachments"}, "ActiveDirectory": {"Domain": "company.local", "Username": "<EMAIL>", "Password": "service-password", "SearchBase": "OU=Users,DC=company,DC=local", "UseSSL": true, "Port": 636, "TimeoutSeconds": 30}, "Cors": {"AllowedOrigins": ["https://localhost:3000", "https://localhost:5173", "https://localhost:4200"]}, "ApiSettings": {"MaxPageSize": 100, "DefaultPageSize": 20, "MaxFileSize": 10485760, "AllowedFileTypes": ["image/jpeg", "image/png", "image/gif", "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "text/plain"]}}